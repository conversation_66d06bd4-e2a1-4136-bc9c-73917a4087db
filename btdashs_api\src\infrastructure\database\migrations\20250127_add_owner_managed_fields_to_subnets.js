/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.withSchema('dtm_base').alterTable('subnets', function(table) {
    // Add columns for owner-managed field tracking
    table.jsonb('owner_managed_fields').defaultTo('{}');
    table.timestamp('owner_managed_at').nullable();
    table.integer('owner_managed_by').nullable().references('id').inTable('dtm_base.users');
    
    // Add indexes
    table.index('owner_managed_by', 'idx_subnets_owner_managed_by');
  }).then(() => {
    // Add GIN index for JSONB column (needs raw SQL)
    return knex.raw(`
      CREATE INDEX IF NOT EXISTS idx_subnets_owner_managed_fields 
      ON dtm_base.subnets USING GIN (owner_managed_fields)
    `);
  }).then(() => {
    // Add comments
    return knex.raw(`
      COMMENT ON COLUMN dtm_base.subnets.owner_managed_fields IS 'JSON object tracking which fields are managed by subnet owner vs system updates';
      COMMENT ON COLUMN dtm_base.subnets.owner_managed_at IS 'Timestamp when owner last updated managed fields';
      COMMENT ON COLUMN dtm_base.subnets.owner_managed_by IS 'User ID of the subnet owner who last updated managed fields';
    `);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.withSchema('dtm_base').alterTable('subnets', function(table) {
    // Drop indexes first
    table.dropIndex('owner_managed_by', 'idx_subnets_owner_managed_by');
  }).then(() => {
    // Drop GIN index
    return knex.raw('DROP INDEX IF EXISTS dtm_base.idx_subnets_owner_managed_fields');
  }).then(() => {
    // Drop columns
    return knex.schema.withSchema('dtm_base').alterTable('subnets', function(table) {
      table.dropColumn('owner_managed_fields');
      table.dropColumn('owner_managed_at');
      table.dropColumn('owner_managed_by');
    });
  });
};
